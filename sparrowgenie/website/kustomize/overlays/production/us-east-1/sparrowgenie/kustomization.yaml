apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: sparrowgenie-website
nameSuffix: -sparrowgenie
labels:
- pairs:
    layer: sparrowgenie
resources:
- cms
- website
- secrets
- *****************:surveysparrow/sparrowgenie-production-config.git//Website/sparrowgenie-website?ref=master
- ../../../../../../ingress-base/production/https-us-east-1/
- arm-karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sg-website
  newTag: master_d88feb6_122
patches:
- path: ingress-patch.yaml
