apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    region: us-east-1
    objects: |
      - objectName: website/strapi/staging
        objectType: secretsmanager
        jmesPath:
          - path: DATABASE_USERNAME
            objectAlias: DATABASE_USERNAME
          - path: DATABASE_PASSWORD
            objectAlias: DATABASE_PASSWORD
          - path: SENTRY_DSN
            objectAlias: SENTRY_DSN
          - path: DRAFT_MODE_SECRET
            objectAlias: DRAFT_MODE_SECRET
          - path: TRANSFER_TOKEN_SALT
            objectAlias: TRANSFER_TOKEN_SALT
          - path: ADMIN_JWT_SECRET
            objectAlias: ADMIN_JWT_SECRET
          - path: API_TOKEN_SALT
            objectAlias: API_TOKEN_SALT
          - path: APP_KEYS
            objectAlias: APP_KEYS
          - path: JWT_SECRET
            objectAlias: JWT_SECRET
  secretObjects:
    - secretName: strapi-secrets
      type: Opaque
      data:
        - objectName: DAT<PERSON>ASE_USERNAME
          key: DATABASE_USERNAME
        - objectName: DATABASE_PASSWORD
          key: DATABASE_PASSWORD
        - objectName: SENTRY_DSN
          key: SENTRY_DSN
        - objectName: DRAFT_MODE_SECRET
          key: DRAFT_MODE_SECRET
        - objectName: TRANSFER_TOKEN_SALT
          key: TRANSFER_TOKEN_SALT
        - objectName: ADMIN_JWT_SECRET
          key: ADMIN_JWT_SECRET
        - objectName: API_TOKEN_SALT
          key: API_TOKEN_SALT
        - objectName: APP_KEYS
          key: APP_KEYS
        - objectName: JWT_SECRET
          key: JWT_SECRET
