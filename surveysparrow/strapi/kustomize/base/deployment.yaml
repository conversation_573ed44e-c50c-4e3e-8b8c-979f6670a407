apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment
  labels:
    app: strapi-server
spec:
  revisionHistoryLimit: 3
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 0
  selector:
    matchLabels:
      app: strapi-server
  template:
    metadata:
      labels:
        app: strapi-server
        application: ss-strapi
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/StrapiServiceRole
    spec:
      serviceAccountName: service-account
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: us-virginia-strapi-secretprovider
      terminationGracePeriodSeconds: 30
      nodeSelector:
        app: strapi-arm
      tolerations:
      - effect: NoSchedule
        operator: Equal
        key: app
        value: squad-env
      containers:
      - name: strapi
        image: strapi:latest
        imagePullPolicy: IfNotPresent
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
          # readOnlyRootFilesystem: true
          seccompProfile:
            type: RuntimeDefault
        resources:
          requests:
            cpu: 700m
            memory: 512Mi
          limits:
            memory: 1024Mi
        ports:
        - containerPort: 1337
          name: strapi-server
        readinessProbe:
          httpGet:
            path: /health
            port: strapi-server
            scheme: HTTP
          initialDelaySeconds: 45
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 2
        livenessProbe:
          httpGet:
            path: /health
            port: strapi-server
            scheme: HTTP
          initialDelaySeconds: 35
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 2
        volumeMounts:
        - name: secrets-store-inline
          mountPath: /mnt/secrets
          readOnly: true
        envFrom:
        - configMapRef:
            name: strapi-envs
        - secretRef:
            name: strapi-secrets
