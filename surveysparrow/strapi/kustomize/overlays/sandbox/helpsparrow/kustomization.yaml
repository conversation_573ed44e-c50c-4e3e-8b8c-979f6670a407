apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: sparrowsuite-

namespace: website-sparrowsuite

resources:
- ../../../base/high-availability
- ../../../base

images:
- name: strapi
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/strapi/sparrowsuite
  newTag: da0d363_605

patches:
- path: deployment_patch.yaml
- path: secrets_patch.yaml

labels:
- includeSelectors: true
  pairs:
    environment: staging
    app: strapi-server
