apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: secretprovider
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: website/strapi/noforms
        objectType: secretsmanager
        jmesPath:
          - path: DATABASE_USERNAME
            objectAlias: DATABASE_USERNAME
          - path: DATABASE_PASSWORD
            objectAlias: DATABASE_PASSWORD
          - path: SENTRY_DSN
            objectAlias: SENTRY_DSN
          - path: DRAFT_MODE_SECRET
            objectAlias: DRAFT_MODE_SECRET
          - path: TRANSFER_TOKEN_SALT
            objectAlias: TRANSFER_TOKEN_SALT
          - path: ADMIN_JWT_SECRET
            objectAlias: ADMIN_JWT_SECRET
          - path: API_TOKEN_SALT
            objectAlias: API_TOKEN_SALT
          - path: APP_KEYS
            objectAlias: APP_KEYS
          - path: JWT_SECRET
            objectAlias: JWT_SECRET
