apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: noforms-

namespace: website-noforms

resources:
- ../../../base/high-availability
- ../../../base
- *****************:surveysparrow/surveysparrow-staging-config.git//website/strapi/noforms?ref=strapi-envs

images:
- name: strapi
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/strapi/noforms
  newTag: test-strapi-docker-optimizations

patches:
- path: deployment_patch.yaml
- path: secrets_patch.yaml

labels:
- includeSelectors: true
  pairs:
    environment: staging
    app: strapi-server
