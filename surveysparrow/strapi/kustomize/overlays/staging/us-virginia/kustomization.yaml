apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: us-virginia-

namespace: website

resources:
- ../../../base/high-availability
- ../../../base
- *****************:surveysparrow/surveysparrow-staging-config.git//US-VIRGINIA/strapi?ref=preproduction-master
- provisioner.yaml

images:
- name: strapi
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/strapi/staging
  newTag: preproduction_f7f36c1a1_17

patches:
- path: idle_hpa_patch.yaml

labels:
- includeSelectors: true
  pairs:
    app: strapi-server
    environment: staging
