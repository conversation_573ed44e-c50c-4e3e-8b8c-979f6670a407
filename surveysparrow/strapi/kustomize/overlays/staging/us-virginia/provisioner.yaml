apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: arm
spec:
  disruption:
    budgets:
    - nodes: 10%
    consolidateAfter: 0s
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: '150'
    memory: 200Gi
  template:
    metadata:
      labels:
        app: strapi-arm
        intent: app
    spec:
      expireAfter: 24h
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: us-virginia-strapi-arm-nc
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
        - spot
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.micro
        - t4g.small
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      taints:
      - effect: NoSchedule
        key: app
        value: squad-env
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: strapi-arm-nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 2
  role: KarpenterNodeRole-ss-staging-cluster
  securityGroupSelectorTerms:
  - tags:
      SurveySparrowK8s: 'true'
  subnetSelectorTerms:
  - tags:
      SurveySparrowK8s: 'true'
  tags:
    Name: Karpenter/Wesbite-arm
    CreatedBy: <EMAIL>
    Service: Strapi
    Team: Website
    Environment: Staging
    BuisnessUnit: SurveySparrow
