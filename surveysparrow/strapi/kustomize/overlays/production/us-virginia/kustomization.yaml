apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: us-virginia-

namespace: website

resources:
- ../../../base/high-availability
- ../../../base
- *****************:surveysparrow/surveysparrow-production-config.git//US-VIRGINIA/strapi?ref=master
- provisioner.yaml
- ../../../../../ingress-base/production/http-us-virginia

images:
- name: strapi
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/strapi/production
  newTag: latest

patches:
- path: secrets_patch.yaml
- path: service_account_patch.yaml
- path: ingress_patch.yaml
- path: idle_hpa_patch.yaml

labels:
- includeSelectors: true
  pairs:
    environment: production
    app: strapi-server
