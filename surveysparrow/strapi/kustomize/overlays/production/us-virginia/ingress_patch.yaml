apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:556123534406:certificate/889b5184-2c93-4c39-b2cf-0906c977132c
    alb.ingress.kubernetes.io/target-node-labels: app=strapi-arm
    alb.ingress.kubernetes.io/load-balancer-name: us-vi-strapi-alb
    alb.ingress.kubernetes.io/group.name: ss-strapi-servers
    alb.ingress.kubernetes.io/security-groups: sg-0234fde2150673ec1
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      idle_timeout.timeout_seconds=1200,
      deletion_protection.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ss-production-alb-logs,
      access_logs.s3.prefix=surveysparrow-us-vi-strapi-alb
    alb.ingress.kubernetes.io/tags: CreatedBy=<EMAIL>,Team=Website,Service=Strapi,Environment=Production,BusinessUnit=SurveySparrow
spec:
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: strapi-service
            port:
              number: 8080
