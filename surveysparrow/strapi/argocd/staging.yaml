apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: surveysparrow-strapi-staging
  namespace: argocd
spec:
  generators:
    - list:
        elements:
          - region: us-virginia
  template:
    metadata:
      name: surveysparrow-strapi-staging-{{region}}
    spec:
      project: default
      source:
        repoURL: *****************:surveysparrow/sparrow-k8s-manifests.git
        targetRevision: master
        path: surveysparrow/strapi/kustomize/overlays/staging/{{region}}
      destination:
        server: https://kubernetes.default.svc
        namespace: website
      syncPolicy:
        automated:
          selfHeal: true
