apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: surveysparrow-strapi-sandbox
  namespace: argocd
spec:
  generators:
    - list:
        elements:
          - environment: noforms
          - environment: sparrowsuite
          - environment: helpsparrow
  template:
    metadata:
      name: surveysparrow-strapi-sandbox-{{environment}}
    spec:
      project: default
      source:
        repoURL: *****************:surveysparrow/sparrow-k8s-manifests.git
        targetRevision: master
        path: surveysparrow/strapi/kustomize/overlays/sandbox/{{environment}}
      destination:
        server: https://kubernetes.default.svc
        namespace: website-{{environment}}
      syncPolicy:
        automated:
          selfHeal: true
