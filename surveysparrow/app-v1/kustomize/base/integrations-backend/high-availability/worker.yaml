apiVersion: apps/v1
kind: Deployment
metadata:
  name: integrations-backend-worker
  labels:
    app: integrations-backend-worker
    application: integrations-backend
    group: integrations-backend-worker
spec:
  selector:
    matchLabels:
      app: integrations-backend-worker
      application: integrations-backend
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: integrations-backend-worker
        application: integrations-backend
        group: integrations-backend-worker
    spec:
      nodeSelector: 
        app: integrations-backend-worker
      containers:
      - name: integrations-backend-worker
        image: ss-integrations-backend-worker:latest
        imagePullPolicy: Always
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/production.json
          subPath: production.json
        env:
        - name: NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED
          value: "100"
        readinessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: 8080
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
          - containerPort: 8080
        resources:
          requests:
            cpu: 3000m
            memory: 4Gi
          limits:
            memory: 8Gi
      volumes:
      - name: config-mount
        configMap:
          name: integrations-backend-config
          items:
          - key: production.json
            path: production.json
