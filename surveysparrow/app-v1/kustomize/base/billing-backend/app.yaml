apiVersion: apps/v1
kind: Deployment
metadata:
  name: billing-backend-application
  labels:
    app: billing-backend-application
    group: billing-backend-application
spec:
  selector:
    matchLabels:
      app: billing-backend-application
      application: billing-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: billing-backend-application
        application: billing-backend
        group: billing-backend-application
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      nodeSelector:
        app: billing-backend-application
      containers:
      - name: billing-backend-application
        image: ss-billing-backend-application:latest
        imagePullPolicy: IfNotPresent
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: config-mount
          mountPath: /app/config/staging.json
          subPath: staging.json
        readinessProbe:
          httpGet:
            path: /status
            port: http
            httpHeaders:
            - name: x-probe
              value: READINESS
          initialDelaySeconds: 30
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /status
            port: http
            httpHeaders:
            - name: x-probe
              value: LIVENESS
          initialDelaySeconds: 40
          timeoutSeconds: 20
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8080
          name: http
        resources:
          requests:
            cpu: 1000m
            memory: 512Mi
      volumes:
      - name: config-mount
        configMap:
          name: billing-backend-config
          items:
          - key: staging.json
            path: staging.json
