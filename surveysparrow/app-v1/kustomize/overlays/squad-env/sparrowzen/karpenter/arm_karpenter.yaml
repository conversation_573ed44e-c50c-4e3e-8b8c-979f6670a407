apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: arm
spec:
  disruption:
    budgets:
    - nodes: 10%
    consolidateAfter: 0s
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: "14"
    memory: 28Gi
  template:
    metadata:
      labels:
        app: sparrowzen-arm
        intent: app
    spec:
      expireAfter: 12h
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: sparrowzen-arm-nc
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - spot
        - on-demand
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t4g.micro
        - t4g.small
        - t4g.medium
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
      - key: kubernetes.io/arch
        operator: In
        values:
        - arm64
      taints:
      - effect: NoSchedule
        key: app
        value: squad-env
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: arm-nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 2
  # need to check for feasibility to maintain same name in all surveysparrow cluster and environment
  role: KarpenterNodeRole-ss-staging-cluster
  securityGroupSelectorTerms:
  - tags:
      SurveySparrowK8s: "true"
  subnetSelectorTerms:
  - tags:
      SurveySparrowK8s: "true"
  tags:
    CreatedBy: <EMAIL>
    Service: App-V1
    Team: Distribution
    app: sparrowzen
    Name: Karpenter/sparrowzen-arm