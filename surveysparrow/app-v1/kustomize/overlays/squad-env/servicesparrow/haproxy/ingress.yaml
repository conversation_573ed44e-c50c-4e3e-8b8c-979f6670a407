apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/load-balancer-name: ss-staging-eks-alb
    alb.ingress.kubernetes.io/group.name: ss-staging
    alb.ingress.kubernetes.io/security-groups: sg-086a204e3eb0f3459
    alb.ingress.kubernetes.io/subnets: subnet-0420050656953dce8, subnet-02e4af8986529e812, subnet-0177bb9a9ec89690c, subnet-0d989b0dab2eb4dee
    alb.ingress.kubernetes.io/target-node-labels: app=servicesparrow-amd
spec:
  ingressClassName: my-aws-ingress-class
  rules:
    - host: '*.servicesparrow.app'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: haproxy-service
                port:
                  number: 8080