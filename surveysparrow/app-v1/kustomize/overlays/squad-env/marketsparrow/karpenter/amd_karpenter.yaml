apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: amd
spec:
  disruption:
    budgets:
    - nodes: 10%
    consolidateAfter: 0s
    consolidationPolicy: WhenEmptyOrUnderutilized
  limits:
    cpu: "150"
    memory: 200Gi
  template:
    metadata:
      labels:
        app: marketsparrow-amd
        intent: app
    spec:
      expireAfter: 24h
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: marketsparrow-amd-nc
      requirements:
      - key: karpenter.sh/capacity-type
        operator: In
        values:
        - on-demand
        - spot
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t3a.micro
        - t3a.small
        - t3a.medium
        - t3a.large
        - t3a.xlarge
        - t3a.2xlarge
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - us-east-1b
      - key: kubernetes.io/os
        operator: In
        values:
        - linux
      - key: kubernetes.io/arch
        operator: In
        values:
        - amd64
      taints:
      - effect: NoSchedule
        key: app
        value: squad-env
---
apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: amd-nc
spec:
  amiFamily: AL2023
  amiSelectorTerms:
  - alias: al2023@latest
  metadataOptions:
    httpPutResponseHopLimit: 2
  role: KarpenterNodeRole-ss-staging-cluster
  securityGroupSelectorTerms:
    - tags:
        SurveySparrowK8s: "true"
  subnetSelectorTerms:
    - tags:
        SurveySparrowK8s: "true"
  blockDeviceMappings:
  - deviceName: /dev/xvda
    ebs:
      volumeSize: 35Gi
      volumeType: gp3
      deleteOnTermination: true
  tags:
    CreatedBy: <EMAIL>
    Service: App-V1
    Team: Deals
    app: marketsparrow
    Name: Karpenter/marketsparrow-amd
