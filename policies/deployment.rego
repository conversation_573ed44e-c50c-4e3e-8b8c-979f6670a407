package main

import rego.v1

# =============================================================================
# Kubernetes Deployment Security and Best Practices Rules
# =============================================================================

# RULE 1: Containers must have securityContext defined
warn contains {
  "msg": sprintf("Container '%v' must have securityContext defined", [container.name]),
  "details": {
    "rule": "security_context_required",
    "container": container.name,
    "field": "securityContext",
    "recommendation": "Add securityContext block to container specification"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not container.securityContext
}

# RULE 2: Containers must not run as root
warn contains {
  "msg": sprintf("Container '%v' must not run as root user", [container.name]),
  "details": {
    "rule": "no_root_user",
    "container": container.name,
    "field": "securityContext.runAsUser",
    "recommendation": "Set runAsUser to a non-zero value or runAsNonRoot to true"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext
  container.securityContext.runAsUser == 0
}

# RULE 3: Containers should have runAsNonRoot set to true
deny contains {
  "msg": sprintf("Container '%v' should have runAsNonRoot set to true", [container.name]),
  "details": {
    "rule": "run_as_non_root",
    "container": container.name,
    "field": "securityContext.runAsNonRoot",
    "recommendation": "Set runAsNonRoot: true in container securityContext"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext
  not container.securityContext.runAsNonRoot == true
}

# RULE 4: Containers should have read-only root filesystem
deny contains {
  "msg": sprintf("Container '%v' should have readOnlyRootFilesystem set to true", [container.name]),
  "details": {
    "rule": "readonly_root_filesystem",
    "container": container.name,
    "field": "securityContext.readOnlyRootFilesystem",
    "recommendation": "Set readOnlyRootFilesystem: true and use volumes for writable directories"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext
  not container.securityContext.readOnlyRootFilesystem == true
}

# RULE 5: Containers should drop ALL capabilities and add only required ones
warn contains {
  "msg": sprintf("Container '%v' should drop ALL capabilities", [container.name]),
  "details": {
    "rule": "drop_all_capabilities",
    "container": container.name,
    "field": "securityContext.capabilities.drop",
    "recommendation": "Set capabilities.drop: ['ALL'] and add only required capabilities"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext
  container.securityContext.capabilities
  not "ALL" in container.securityContext.capabilities.drop
}

# RULE 6: Containers should have capabilities defined if securityContext exists
warn contains {
  "msg": sprintf("Container '%v' should define capabilities (drop: ['ALL'] recommended)", [container.name]),
  "details": {
    "rule": "capabilities_recommended",
    "container": container.name,
    "field": "securityContext.capabilities",
    "recommendation": "Add capabilities block and drop ALL by default"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext
  not container.securityContext.capabilities
}

# Helper function to check if deployment is exempt from resource limits
is_resource_exempt(deployment_name, labels) if {
  # Exempt deployments with specific labels
  labels["resource-limits"] == "exempt"
}

is_resource_exempt(deployment_name, labels) if {
  # Exempt specific deployment types
  deployment_types := {"worker", "cronjob", "migration", "init"}
  some dtype in deployment_types
  contains(lower(deployment_name), dtype)
}

is_resource_exempt(deployment_name, labels) if {
  # Exempt Node.js applications that might have memory issues with limits
  labels["app.kubernetes.io/component"] == "nodejs"
}

is_resource_exempt(deployment_name, labels) if {
  # Exempt based on custom label
  labels["deployment-type"] in {"worker", "batch", "migration"}
}

# RULE 7: Containers must have resource limits (with exemptions)
warn contains {
  "msg": sprintf("Container '%v' must have CPU and memory limits", [container.name]),
  "details": {
    "rule": "required_resource_limits",
    "container": container.name,
    "field": "resources.limits",
    "missing_limits": missing_limits,
    "recommendation": "Set both CPU and memory limits to prevent resource exhaustion, or add 'resource-limits: exempt' label"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not is_resource_exempt(input.metadata.name, input.metadata.labels)
  
  # Check if resources and limits exist
  not container.resources.limits
  
  required_limits := {"cpu", "memory"}
  missing_limits := required_limits
}

# RULE 8: Containers must have resource requests (with exemptions)
deny contains {
  "msg": sprintf("Container '%v' must have CPU and memory requests", [container.name]),
  "details": {
    "rule": "required_resource_requests",
    "container": container.name,
    "field": "resources.requests",
    "missing_requests": missing_requests,
    "recommendation": "Set both CPU and memory requests for proper scheduling, or add 'resource-limits: exempt' label"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not is_resource_exempt(input.metadata.name, input.metadata.labels)
  
  # Check if resources and requests exist
  not container.resources.requests
  
  required_requests := {"cpu", "memory"}
  missing_requests := required_requests
}

# RULE 9: Images should not use 'latest' tag
warn contains {
  "msg": sprintf("Container '%v' should not use 'latest' tag: %v", [container.name, container.image]),
  "details": {
    "rule": "no_latest_tag",
    "container": container.name,
    "field": "image",
    "current_image": container.image,
    "recommendation": "Use specific version tags for reproducible deployments"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  endswith(container.image, ":latest")
}

# RULE 10: Images should use specific tags (not just image name)
deny contains {
  "msg": sprintf("Container '%v' should use specific image tag: %v", [container.name, container.image]),
  "details": {
    "rule": "specific_image_tag",
    "container": container.name,
    "field": "image",
    "current_image": container.image,
    "recommendation": "Specify a version tag (e.g., myapp:1.2.3)"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not contains(container.image, ":")
}

# RULE 11: Images should be from trusted registries
trusted_registries := [
  "713859105457.dkr.ecr.us-east-1.amazonaws.com/",
  "556123534406.dkr.ecr.us-east-1.amazonaws.com/",
  "396913698328.dkr.ecr.us-east-1.amazonaws.com/",
  "288761754283.dkr.ecr.us-east-1.amazonaws.com/",
  "public.ecr.aws/",
  "axllent/mailpit:v1.20"
]

deny contains {
  "msg": sprintf("Container '%v' must use images from trusted registries: %v", [container.name, container.image]),
  "details": {
    "rule": "trusted_image_registry",
    "container": container.name,
    "field": "image",
    "current_image": container.image,
    "trusted_registries": trusted_registries
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not image_from_trusted_registry(container.image)
}

# Helper function to check trusted registries
image_from_trusted_registry(image) if {
  some registry in trusted_registries
  startswith(image, registry)
}

# RULE 12: Containers should have liveness probes
warn contains {
  "msg": sprintf("Container '%v' should have a liveness probe for better reliability", [container.name]),
  "details": {
    "rule": "liveness_probe_recommended",
    "container": container.name,
    "field": "livenessProbe",
    "recommendation": "Add liveness probe to detect and restart unhealthy containers"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not container.livenessProbe
}

# RULE 13: Containers should have readiness probes
warn contains {
  "msg": sprintf("Container '%v' should have a readiness probe for better reliability", [container.name]),
  "details": {
    "rule": "readiness_probe_recommended",
    "container": container.name,
    "field": "readinessProbe",
    "recommendation": "Add readiness probe to prevent routing traffic to unhealthy containers"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  not container.readinessProbe
}

# RULE 14: Deployments should have required labels
required_deployment_labels := [
  "app",
  "environment"
]

deny contains {
  "msg": sprintf("Deployment missing required label: %v", [missing_label]),
  "details": {
    "rule": "required_deployment_labels",
    "field": "metadata.labels",
    "missing_label": missing_label,
    "required_labels": required_deployment_labels
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some missing_label in required_deployment_labels
  not input.metadata.labels[missing_label]
}

# RULE 16: Deployments should not use hostNetwork
deny contains {
  "msg": "Deployment should not use hostNetwork for security reasons",
  "details": {
    "rule": "no_host_network",
    "field": "spec.template.spec.hostNetwork",
    "current_value": input.spec.template.spec.hostNetwork,
    "recommendation": "Remove hostNetwork or set to false"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  input.spec.template.spec.hostNetwork == true
}

# RULE 17: Deployments should not use hostPID
deny contains {
  "msg": "Deployment should not use hostPID for security reasons",
  "details": {
    "rule": "no_host_pid",
    "field": "spec.template.spec.hostPID",
    "current_value": input.spec.template.spec.hostPID,
    "recommendation": "Remove hostPID or set to false"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  input.spec.template.spec.hostPID == true
}

# RULE 18: Deployments should not use hostIPC
deny contains {
  "msg": "Deployment should not use hostIPC for security reasons",
  "details": {
    "rule": "no_host_ipc",
    "field": "spec.template.spec.hostIPC", 
    "current_value": input.spec.template.spec.hostIPC,
    "recommendation": "Remove hostIPC or set to false"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  input.spec.template.spec.hostIPC == true
}

# Helper function to check if deployment is a worker type
is_worker_deployment(deployment_name, labels) if {
  worker_types := {"worker", "batch", "job", "cronjob", "migration"}
  some wtype in worker_types
  contains(lower(deployment_name), wtype)
}

is_worker_deployment(deployment_name, labels) if {
  labels["deployment-type"] in {"worker", "batch", "job"}
}

# Helper function to check if deployment is exempt from resource requirements
is_resource_exempt(deployment_name, labels) if {
  labels["resource-limits"] == "exempt"
}

# RULE 19: Deployments should use appropriate strategy (RollingUpdate for web apps, Recreate allowed for workers)
warn contains {
  "msg": "Consider using RollingUpdate strategy for zero-downtime deployments",
  "details": {
    "rule": "rolling_update_strategy",
    "field": "spec.strategy.type",
    "current_value": input.spec.strategy.type,
    "recommendation": "Use RollingUpdate strategy for web applications, Recreate is acceptable for workers"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  input.spec.strategy.type == "Recreate"
  not is_worker_deployment(input.metadata.name, input.metadata.labels)
}

# RULE 20: Containers should not have environment variables with sensitive names
sensitive_env_patterns := ["password", "secret", "key", "token", "credential"]

deny contains {
  "msg": sprintf("Container '%v' has potentially sensitive environment variable: %v", [container.name, env.name]),
  "details": {
    "rule": "no_sensitive_env_vars",
    "container": container.name,
    "field": "env",
    "env_var": env.name,
    "recommendation": "Use Kubernetes secrets or configMaps for sensitive data"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  some env in container.env
  some pattern in sensitive_env_patterns
  contains(lower(env.name), pattern)
  env.value  # Direct value instead of valueFrom
}

# RULE 21: Containers should not run with privileged security context
deny contains {
  "msg": sprintf("Container '%v' should not run with privileged security context", [container.name]),
  "details": {
    "rule": "no_privileged_containers",
    "container": container.name,
    "field": "securityContext.privileged",
    "recommendation": "Remove privileged: true from securityContext"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext.privileged == true
}

# RULE 22: Containers should have seccompProfile set to RuntimeDefault
warn contains {
  "msg": sprintf("Container '%v' should have seccompProfile set to RuntimeDefault", [container.name]),
  "details": {
    "rule": "seccomp_profile_recommended",
    "container": container.name,
    "field": "securityContext.seccompProfile",
    "recommendation": "Add seccompProfile.type: RuntimeDefault to securityContext"
  }
} if {
  input.kind == "Deployment"
  input.apiVersion == "apps/v1"
  some container in input.spec.template.spec.containers
  container.securityContext
  not container.securityContext.seccompProfile
}

# RULE 24: NodePool should have appropriate resource limits
deny contains {
  "msg": sprintf("NodePool '%v' has insufficient CPU limits for production", [input.metadata.name]),
  "details": {
    "rule": "nodepool_cpu_limits",
    "field": "spec.limits.cpu",
    "current_value": input.spec.limits.cpu,
    "recommendation": "Set CPU limits to at least 20 for production workloads"
  }
} if {
  input.kind == "NodePool"
  input.apiVersion == "karpenter.sh/v1"
  to_number(input.spec.limits.cpu) < 20
  input.metadata.labels.environment in {"production", "prod"}
}

# RULE 25: NodePool should have appropriate memory limits
deny contains {
  "msg": sprintf("NodePool '%v' has insufficient memory limits for production", [input.metadata.name]),
  "details": {
    "rule": "nodepool_memory_limits",
    "field": "spec.limits.memory",
    "current_value": input.spec.limits.memory,
    "recommendation": "Set memory limits to at least 40Gi for production workloads"
  }
} if {
  input.kind == "NodePool"
  input.apiVersion == "karpenter.sh/v1"
  not endswith(input.spec.limits.memory, "Gi")
  input.metadata.labels.environment in {"production", "prod"}
}

deny contains {
  "msg": sprintf("NodePool '%v' has insufficient memory limits for production", [input.metadata.name]),
  "details": {
    "rule": "nodepool_memory_limits",
    "field": "spec.limits.memory",
    "current_value": input.spec.limits.memory,
    "recommendation": "Set memory limits to at least 40Gi for production workloads"
  }
} if {
  input.kind == "NodePool"
  input.apiVersion == "karpenter.sh/v1"
  endswith(input.spec.limits.memory, "Gi")
  to_number(trim_suffix(input.spec.limits.memory, "Gi")) < 40
  input.metadata.labels.environment in {"production", "prod"}
}

# Helper function to check if any string in a list contains any substring from another list
contains_any(strings, substrings) if {
  some string in strings
  some substring in substrings
  contains(string, substring)
}

# =============================================================================
# Summary Functions
# =============================================================================

# Count total violations
deployment_violation_count := count(deny)

# Count total warnings
deployment_warning_count := count(warn)

# Security score (percentage of security rules passed)
security_rules := 15  # Security-focused rules
security_violations := count([violation | some violation in deny; violation.details.rule in {
  "security_context_required", "no_root_user", "run_as_non_root", "readonly_root_filesystem", 
  "drop_all_capabilities", "specific_image_tag", "trusted_image_registry", "no_privileged_ports", 
  "no_host_network", "no_host_pid", "no_host_ipc", "no_sensitive_env_vars", "no_privileged_containers",
  "no_privilege_escalation"
}])

security_score := ((security_rules - security_violations) / security_rules) * 100
