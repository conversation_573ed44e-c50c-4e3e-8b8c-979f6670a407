apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpcenter-application
spec:
  selector:
    matchLabels:
      app: helpcenter
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: helpcenter
    spec:
      terminationGracePeriodSeconds: 30
      serviceAccountName: helpcenter-sa
      containers:
      - name: helpcenter-app
        image: helpcenter:app
        imagePullPolicy: IfNotPresent
        args: ['server.js']
        lifecycle:
          preStop:
            exec:
              command: ['/bin/sh', '-c', 'sleep 5']
        readinessProbe:
          httpGet:
            path: /hc/api/health
            port: 8005
          initialDelaySeconds: 30
          timeoutSeconds: 15
          periodSeconds: 30
          failureThreshold: 5
          successThreshold: 2
        livenessProbe:
          httpGet:
            path: /hc/api/health
            port: 8005
          initialDelaySeconds: 40
          timeoutSeconds: 15
          periodSeconds: 60
          failureThreshold: 5
          successThreshold: 1
        ports:
        - containerPort: 8005
