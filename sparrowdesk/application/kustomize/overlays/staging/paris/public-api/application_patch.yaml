apiVersion: apps/v1
kind: Deployment
metadata:
  name: public-api
spec:
  template:
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: paris-public-api-secrets
      containers:
        - name: public-api-app
          envFrom:
            - configMapRef:
                name: paris-public-api-configmap
            - secretRef:
                name: paris-public-api-secrets
          securityContext:
            runAsNonRoot: true
            readOnlyRootFilesystem: true
          resources:
            requests:
              cpu: 300m
              memory: 600Mi
            limits:
              memory: 600Mi
