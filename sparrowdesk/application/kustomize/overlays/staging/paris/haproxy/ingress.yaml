apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:396913698328:certificate/087f535a-a9bb-498c-9942-783ee880d773
spec:
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'ssl-redirect'
                port:
                  name: 'use-annotation'
    - host: 'websocket.paris.campaignsparrow.com'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'paris-websocket-application-service'
                port:
                  number: 8800
          - path: /robots.txt
            pathType: Exact
            backend:
              service:
                name: 'robots-response'
                port:
                  name: 'use-annotation'
    - host: '*.paris.campaignsparrow.com'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'paris-haproxy-service'
                port:
                  number: 8081
          - path: /robots.txt
            pathType: Exact
            backend:
              service:
                name: 'robots-response'
                port:
                  name: 'use-annotation'
