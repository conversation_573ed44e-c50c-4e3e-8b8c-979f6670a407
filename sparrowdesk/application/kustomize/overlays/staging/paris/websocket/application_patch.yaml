apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-application
spec:
  template:
    spec:
      containers:
        - name: websocket-app
          securityContext:
            runAsNonRoot: true
            readOnlyRootFilesystem: true
          envFrom:
            - configMapRef:
                name: paris-websocket-configmap
          resources:
            requests:
              cpu: 300m
              memory: 600Mi
            limits:
              cpu: 600m
              memory: 600Mi
