apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../../../../base/websocket
labels:
- pairs:
    app: websocket
    environment: staging
images:
- name: websocket:app
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/desk-paris-ecr
  newTag: websocket_slack-ss-notification-test_14926fb_10
patches:
- path: application_patch.yaml
- path: application_replicas.yaml
- path: service_account.yaml
