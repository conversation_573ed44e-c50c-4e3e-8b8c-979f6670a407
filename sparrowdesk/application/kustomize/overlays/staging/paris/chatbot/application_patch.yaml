apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-application
spec:
  template:
    spec:
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: paris-chatbot-secrets
      containers:
        - name: chatbot-app
          envFrom:
            - configMapRef:
                name: paris-chatbot-configmap
            - secretRef:
                name: paris-chatbot-secrets
          securityContext:
            runAsNonRoot: true
            readOnlyRootFilesystem: true
          resources:
            requests:
              cpu: 500m
              memory: 500Mi
            limits:
              cpu: 1000m
              memory: 500Mi
