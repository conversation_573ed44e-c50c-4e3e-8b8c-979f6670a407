apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpdesk-application
  labels:
    app: helpdesk
    environment: staging
spec:
  selector:
    matchLabels:
      app: helpdesk
  template:
    metadata:
      labels:
        app: helpdesk
        environment: staging
    spec:
      volumes:
      - name: app-config
        configMap:
          name: paris-helpdesk-configmap
          items:
          - key: staging.json
            path: staging.json
          defaultMode: 420
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: paris-helpdesk-secrets
      containers:
      - name: helpdesk-app
        securityContext:
          runAsNonRoot: true
          readOnlyRootFilesystem: true
        envFrom:
        - configMapRef:
            name: paris-helpdesk-configmap
        - secretRef:
            name: paris-helpdesk-secrets
        env:
        - name: VERSION
          value: 2af384c091b9bf206471cd0676689013a3beccbe_1
        resources:
          requests:
            cpu: 500m
            memory: 600Mi
          limits:
            cpu: 1000m
            memory: 600Mi
        volumeMounts:
        - name: app-config
          mountPath: /app/application/helpdesk-application/config/env/staging.json
          subPath: staging.json
