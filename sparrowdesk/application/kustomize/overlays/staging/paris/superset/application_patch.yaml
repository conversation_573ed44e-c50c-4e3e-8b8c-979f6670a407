apiVersion: apps/v1
kind: Deployment
metadata:
  name: superset-application
spec:
  template:
    spec:
      volumes:
        - name: paris-superset-configmap
          configMap:
            name: paris-superset-configmap
            defaultMode: 420
        - name: paris-superset-bootstrap-configmap
          configMap:
            name: paris-superset-bootstrap-configmap
            defaultMode: 420
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: paris-superset-secrets
        - name: superset-config
          configMap:
            name: superset-config
            defaultMode: 420
      containers:
        - name: superset-app
          ports:
            - name: http
              containerPort: 8088
              protocol: TCP
          envFrom:
            - configMapRef:
                name: paris-superset-env-configmap
            - secretRef:
                name: paris-superset-secrets
          env:
            - name: SUPERSET_PORT
              value: '8088'
          resources:
            requests:
              cpu: 500m
              memory: 1000Mi
            limits:
              cpu: 1000m
              memory: 1000Mi
          volumeMounts:
            - name: paris-superset-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_config.py
              subPath: superset_config.py
            - name: paris-superset-bootstrap-configmap
              readOnly: true
              mountPath: /app/pythonpath/superset_bootstrap.sh
              subPath: superset_bootstrap.sh
            - name: secrets-store-inline
              readOnly: true
              mountPath: /mnt/secrets
