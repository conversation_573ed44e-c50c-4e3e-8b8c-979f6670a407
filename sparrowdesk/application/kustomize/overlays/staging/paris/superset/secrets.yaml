apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: superset-secrets
spec:
  provider: aws
  parameters:
    objects: |
      - objectName: "desk-paris-secret"
        objectType: "secretsmanager"
        jmesPath:

          - path: SUPERSET_DB_PASS
            objectAlias: SUPERSET_DB_PASS-Alias

          - path: SUPERSET_GOOGLE_KEY
            objectAlias: SUPERSET_GOOGLE_KEY-Alias

          - path: SUPERSET_GOOGLE_SECRET
            objectAlias: SUPERSET_GOOGLE_SECRET-Alias

          - path: SUPERSET_SECRET_KEY
            objectAlias: SUPERSET_SECRET_KEY-Alias

          - path: SUPERSET_ACCESS_TOKEN
            objectAlias: SUPERSET_ACCESS_TOKEN-Alias

          - path: SUPERSET_REFRESH_TOKEN
            objectAlias: SUPERSET_REFRESH_TOKEN-Alias

  secretObjects:
    - secretName: paris-superset-secrets
      type: Opaque
      data:
        - objectName: 'SUPERSET_DB_PASS-<PERSON><PERSON>'
          key: 'DB_PASS'

        - objectName: 'SUPERSET_GOOGLE_KEY-<PERSON><PERSON>'
          key: 'GOOGLE_KEY'

        - objectName: 'SUPERSET_GOOGLE_SECRET-Alias'
          key: 'GOOGLE_SECRET'

        - objectName: 'SUPERSET_SECRET_KEY-Alias'
          key: 'SECRET_KEY'

        - objectName: 'SUPERSET_ACCESS_TOKEN-Alias'
          key: 'ACCESS_TOKEN'

        - objectName: 'SUPERSET_REFRESH_TOKEN-Alias'
          key: 'REFRESH_TOKEN'
