apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../../../../base/public-api
- ../../../../base/public-api/high-availability
- secrets.yaml
- karpenter.yaml
labels:
- pairs:
    app: public-api
    environment: staging
images:
- name: public-api:app
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sd-berlin
  newTag: public-api_release_18july2025_8b09c275_42
patches:
- path: application_patch.yaml
- path: application_replicas.yaml
- path: service_account.yaml
