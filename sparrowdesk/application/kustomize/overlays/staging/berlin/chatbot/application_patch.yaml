apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-application
spec:
  template:
    spec:
      nodeSelector:
        app: chatbot-arm
        intent: app
      tolerations:
        - key: 'app'
          operator: 'Equal'
          value: 'chatbot-app'
          effect: 'NoSchedule'
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: berlin-chatbot-secrets
      containers:
        - name: chatbot-app
          envFrom:
            - configMapRef:
                name: berlin-chatbot-configmap
            - secretRef:
                name: berlin-chatbot-secrets
          resources:
            requests:
              cpu: 500m
              memory: 500Mi
            limits:
              cpu: 1000m
              memory: 500Mi
          securityContext:
            readOnlyRootFilesystem: true
            runAsNonRoot: true
