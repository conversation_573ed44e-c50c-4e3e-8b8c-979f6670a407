apiVersion: apps/v1
kind: Deployment
metadata:
  name: helpcenter-application
spec:
  template:
    spec:
      nodeSelector:
        app: 'helpcenter-arm'
        intent: 'app'
      tolerations:
        - key: 'app'
          operator: 'Equal'
          value: 'helpcenter-app'
          effect: 'NoSchedule'
      containers:
        - name: helpcenter-app
          envFrom:
            - configMapRef:
                name: berlin-helpcenter-configmap
          resources:
            requests:
              cpu: 500m
              memory: 500Mi
            limits:
              cpu: 1000m
              memory: 500Mi
