apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: >-
      arn:aws:acm:us-east-1:396913698328:certificate/56d8129a-8ad6-4031-9770-b50bfc0fbfe9
spec:
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'ssl-redirect'
                port:
                  name: 'use-annotation'
    - host: 'websocket.berlin.campaignsparrow.com'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'berlin-websocket-application-service'
                port:
                  number: 8800
          - path: /robots.txt
            pathType: Exact
            backend:
              service:
                name: 'robots-response'
                port:
                  name: 'use-annotation'
    - host: '*.berlin.campaignsparrow.com'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'berlin-haproxy-service'
                port:
                  number: 8081
          - path: /robots.txt
            pathType: Exact
            backend:
              service:
                name: 'robots-response'
                port:
                  name: 'use-annotation'
    - host: 'desktest.marketsparrow.com'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: 'berlin-haproxy-service'
                port:
                  number: 8081
