apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-application
spec:
  template:
    spec:
      nodeSelector:
        app: websocket-arm
        intent: app
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "websocket-app"
          effect: "NoSchedule"
      containers:
        - name: websocket-app
          envFrom:
            - configMapRef:
                name: berlin-websocket-configmap
          resources:
            requests:
              cpu: 300m
              memory: 600Mi
            limits:
              cpu: 600m
              memory: 600Mi
