#!/bin/bash

# Check if correct number of arguments are provided
if [ $# -ne 2 ]; then
    echo "Usage: $0 <new_folder_name> <certificate_arn>"
    echo "Example: $0 salem asdfadsf:5432:23-430"
    exit 1
fi

NEW_FOLDER_NAME=$1
CERTIFICATE_ARN=$2
SOURCE_FOLDER="paris"
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if source folder exists
if [ ! -d "$CURRENT_DIR/$SOURCE_FOLDER" ]; then
    echo "Error: Source folder '$SOURCE_FOLDER' does not exist in $CURRENT_DIR"
    exit 1
fi

# Check if destination folder already exists
if [ -d "$CURRENT_DIR/$NEW_FOLDER_NAME" ]; then
    echo "Error: Destination folder '$NEW_FOLDER_NAME' already exists"
    exit 1
fi

echo "Replicating $SOURCE_FOLDER to $NEW_FOLDER_NAME..."

# Copy the entire folder structure
cp -r "$CURRENT_DIR/$SOURCE_FOLDER" "$CURRENT_DIR/$NEW_FOLDER_NAME"

# Find all files in the new folder and replace 'paris' with new folder name
find "$CURRENT_DIR/$NEW_FOLDER_NAME" -type f -exec sed -i "s/paris/$NEW_FOLDER_NAME/g" {} \;

# Replace the certificate-arn annotation in haproxy/ingress.yaml
HAPROXY_INGRESS="$CURRENT_DIR/$NEW_FOLDER_NAME/haproxy/ingress.yaml"
if [ -f "$HAPROXY_INGRESS" ]; then
    # Replace the certificate-arn annotation value
    sed -i "s|arn:aws:acm:us-east-1:396913698328:certificate/[^[:space:]]*|$CERTIFICATE_ARN|g" "$HAPROXY_INGRESS"
    echo "Updated certificate-arn in $HAPROXY_INGRESS"
else
    echo "Warning: haproxy/ingress.yaml not found in $NEW_FOLDER_NAME"
fi

# Navigate to argocd/staging directory and handle paris.yaml
echo "Navigating to argocd/staging directory..."
cd "$CURRENT_DIR/../../../argocd/staging"
echo "Current working directory: $(pwd)"

ARGOCD_SOURCE_FILE="paris.yaml"
ARGOCD_DEST_FILE="$NEW_FOLDER_NAME.yaml"

# Check if source file exists
if [ ! -f "$ARGOCD_SOURCE_FILE" ]; then
    echo "Error: Source file '$ARGOCD_SOURCE_FILE' does not exist in $(pwd)"
    exit 1
fi

# Check if destination file already exists
if [ -f "$ARGOCD_DEST_FILE" ]; then
    echo "Error: Destination file '$ARGOCD_DEST_FILE' already exists"
    exit 1
fi

echo "Replicating $ARGOCD_SOURCE_FILE to $ARGOCD_DEST_FILE..."

# Copy the file
cp "$ARGOCD_SOURCE_FILE" "$ARGOCD_DEST_FILE"

# Replace all occurrences of 'paris' with new folder name in the argocd file
sed -i "s/paris/$NEW_FOLDER_NAME/g" "$ARGOCD_DEST_FILE"

echo "Successfully replicated $ARGOCD_SOURCE_FILE to $ARGOCD_DEST_FILE"

echo "Successfully replicated $SOURCE_FOLDER to $NEW_FOLDER_NAME"
echo "All occurrences of '$SOURCE_FOLDER' have been replaced with '$NEW_FOLDER_NAME'"
echo "Certificate ARN has been updated to: $CERTIFICATE_ARN"
