apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: sparrowdeskstaging
namePrefix: sparrowdeskstaging-
labels:
- pairs:
    layer: sparrowdeskstaging
    environment: staging
resources:
- cms
- website
- secrets
- *****************:surveysparrow/sparrowdesk-staging-config.git//website?ref=main
- ../../../../../../ingress-base/staging/http-us-east-1/
- arm_karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sd-website
  newTag: new-home_ccd76e3_82
patches:
- path: ingress-patch.yaml
