apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: sparrowdesk-website
namePrefix: sparrowdesk-
labels:
- pairs:
    layer: sparrowdesk
    environment: production
resources:
- cms
- website
- secrets
- *****************:surveysparrow/sparrowdesk-production-config.git//website?ref=main
- ../../../../../../ingress-base/production/http-us-east-1/
- arm_karpenter.yaml
- service-account.yaml
- namespace.yaml
images:
- name: website
  newName: ************.dkr.ecr.us-east-1.amazonaws.com/sd-website
  newTag: new-home_ccd76e3_92
patches:
- path: ingress-patch.yaml
