apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: sparrowpay-wingssparrow
namePrefix: wingssparrow-sparrowpay-

resources:
- ../../../../base/payments
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/sparrowpay/wingssparrow/payments?ref=master

patches:
- path: secrets_patch.yaml
- path: deployment_patch.yaml

images:
- name: payments
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/sparrowpay-wingssparrow/payments
  newTag: master_da88e41310

labels:
- includeSelectors: true
  pairs:
    environment: staging
