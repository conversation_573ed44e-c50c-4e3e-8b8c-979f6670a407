apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base
- ../../../base/high-availability
- provisioner.yaml
- *****************:surveysparrow/surveysparrow-staging-config.git//platforms/attainment/US-VIRGINIA?ref=preproduction-master
- ../../../../../ingress-base/staging/http-us-virginia

namespace: attainment

namePrefix: us-virginia-attainment-

images:
- name: application
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/attainment/application
  newTag: SSE-44578-SparrowAttainmentFrontend_8e133cd18

patches:
- path: ingress_patch.yaml
