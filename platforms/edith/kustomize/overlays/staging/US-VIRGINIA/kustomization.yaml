apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../base
- ../../../../../ingress-base/staging/http-us-virginia
- mailpit-a
- mailpit-b
- provisioner.yaml
- pdb.yaml
- *****************:surveysparrow/surveysparrow-staging-config.git/platforms/edith/US-VIRGINIA?ref=preproduction-master

namePrefix: us-virginia-edith-

namespace: edith

images:
- name: admin-server
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/edith/admin-server
  newTag: preproduction-admin-server-14jul2025-edith-1_ea240f5434
- name: api-server
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/edith/api-server
  newTag: preproduction-api-server-14jul2025-edith-2_b25df49439
- name: worker
  newName: 713859105457.dkr.ecr.us-east-1.amazonaws.com/edith/worker
  newTag: preproduction-worker-14jul2025-edith-3_611b2ba438

patches:
- path: ingress.yaml
